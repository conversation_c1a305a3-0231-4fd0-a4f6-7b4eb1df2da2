{"format_version": "1.20.50", "minecraft:attachable": {"description": {"identifier": "ptd_lmhc:cookie_waffle", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ptd/lm_hc/items/food/cookie_waffle", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.cookie_waffle"}, "animations": {"hold_first_person": "animation.ptd_lmhc.food_cookie_waffle.hold_first_person", "hold_third_person": "animation.ptd_lmhc.food_cookie_waffle.hold_third_person", "eat": "animation.ptd_lmhc.food_cookie_waffle.eat", "eat_controller": "controller.animation.ptd_lmhc.food_eat", "general": "controller.animation.ptd_lmhc.food_general"}, "scripts": {"animate": ["general", "eat_controller"]}, "render_controllers": ["controller.render.food"]}}}